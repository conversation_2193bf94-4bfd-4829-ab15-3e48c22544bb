
**PRD (产品需求文档): 系统 UI/UX 优化 (Apple 风格)**

**1. 文档信息**

*   **项目名称:** [你的项目名称] - 系统 UI/UX 优化
*   **版本号:** 1.0
*   **创建日期:** [YYYY-MM-DD]
*   **最后更新日期:** [YYYY-MM-DD]
*   **创建人:** [你的名字/团队]
*   **负责人:** [项目负责人]
*   **相关人员:** [设计师]、[前端开发工程师]、[产品经理]

**2. 项目背景与目标**

*   **2.1 项目背景:**
    *   当前系统界面在视觉美感、用户体验及品牌感方面存在较大提升空间。用户反馈界面较为朴素、功能堆砌，缺乏设计感和精致度，部分操作不够直观。
    *   当前技术栈为 React + TypeScript，为实现现代化 UI 提供了良好基础。
*   **2.2 项目目标:**
    *   **提升视觉美感:** 引入 Apple 设计风格，打造简洁、干净、现代化的用户界面，提升整体的专业感和舒适度。
    *   **优化用户体验:** 改善信息层级，增强操作引导性，提高界面的易用性和操作效率。
    *   **增强品牌一致性:** （如果适用）通过统一的视觉风格，传递更专业的品牌形象。
    *   **提高开发效率:** 建立一套可复用的 UI 组件和设计规范，方便后续功能迭代和维护。

**3. 现有问题分析 (痛点)**

*   **3.1 色彩与对比度:**
    *   色彩方案单调，大面积使用浅灰色与白色，缺乏层次感和视觉焦点。
    *   主色调（蓝色）运用单一，对比度有时不足，影响可读性和操作元素的辨识度。
*   **3.2 布局与间距:**
    *   信息密度不均，部分区域拥挤，部分区域过于空旷。
    *   元素间距和内边距不足，缺乏“呼吸感”。
    *   对齐和视觉流不够规整。
*   **3.3 视觉层级与引导性:**
    *   标题、正文、按钮、标签等元素的视觉区分度不够，重点不突出。
    *   操作按钮样式单一，未有效区分主要和次要操作。
*   **3.4 组件样式:**
    *   卡片、按钮、输入框、标签等基础组件样式较为粗糙，缺乏精致感（如圆角、阴影运用不当或缺失）。
    *   部分特定组件（如侧边栏底部用户信息区域）设计不明确或突兀。
*   **3.5 整体感受:**
    *   功能性有余，美观性和情感化设计不足。

**4. 需求详述 - 整体设计规范 (Apple 风格)**

*   **4.1 设计原则:**
    *   **简洁干净 (Clean & Simple):** 精炼界面元素，避免不必要的装饰。
    *   **内容优先 (Content-focused):** 设计服务于内容呈现和核心功能。
    *   **空间感 (Generous Whitespace):** 大量运用留白，创造舒适的视觉体验。
    *   **优质排版 (Excellent Typography):** 清晰易读的字体层级。
    *   **圆角与柔和 (Rounded Corners & Softness):** 广泛使用柔和的圆角和细腻的阴影。
    *   **中性色调与点缀 (Neutral Palette with Accent):** 以中性色（灰阶）为主，配合清晰的品牌强调色。
    *   **一致性 (Consistency):** 统一的图标、控件和交互模式。

*   **4.2 色彩方案:**
    *   **主背景色:** 非常浅的灰色 (如 `#F5F5F7`) 或纯白 (`#FFFFFF`)。
    *   **卡片/内容区背景色:** 与主背景形成对比 (如主背景为浅灰，卡片为纯白)。
    *   **侧边栏背景色:** 稍深于主内容区的灰色 (如 `#E8E8ED`) 或考虑毛玻璃效果（技术评估后决定）。
    *   **主要文字色:** 深灰色 (如 `#1D1D1F` 或 `#333333`)。
    *   **次要文字/辅助信息色:** 中灰色 (如 `#6E6E73` 或 `#888888`)。
    *   **强调色 (品牌色):** 清晰的蓝色 (如 `#007AFF`)，用于主要操作、选中状态、链接等。
    *   **辅助色/状态色:**
        *   成功/启用: 绿色 (如 `#34C759`)
        *   警告/待定: 橙色/黄色 (如 `#FF9500`)
        *   错误/禁用: 红色 (如 `#FF3B30`)
        *   （以上颜色需根据 Apple HIG 微调）

*   **4.3 字体与排版:**
    *   **字体家族:** 首选 `SF Pro Text` / `SF Pro Display`。备选 `Inter`, `Manrope`, `Noto Sans SC` (中文)。
    *   **字号层级 (示例):**
        *   页面大标题: 28-34px, Semi-bold/Bold
        *   区域/卡片标题: 17-20px, Medium/Semi-bold
        *   正文/描述: 14-16px, Regular
        *   标签/辅助文字: 12-13px, Regular/Medium
    *   **行高:** 1.5 - 1.7 倍字号。
    *   **字间距/字母间距:** 根据字体特性微调，保持阅读舒适性。

*   **4.4 布局与间距:**
    *   **栅格系统:** 考虑引入栅格系统辅助布局对齐。
    *   **基础间距单位:** 推荐使用 4px 或 8px 的倍数 (如 4, 8, 12, 16, 20, 24, 32px)。
    *   **卡片内边距 (Padding):** 16px - 24px。
    *   **元素外边距 (Margin):** 根据视觉需要设置，确保足够的呼吸空间。

*   **4.5 基础组件样式:**
    *   **卡片 (Cards):**
        *   圆角: 8px - 12px。
        *   阴影: 非常柔和、弥散的灰色阴影 (如 `box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.05), 0px 1px 3px rgba(0, 0, 0, 0.08);`)。
        *   边框: 通常无边框，或 1px 浅灰色边框。
    *   **按钮 (Buttons):**
        *   圆角: 6px - 8px。
        *   高度/内边距: 确保视觉舒适和点击区域足够。
        *   **主要按钮:** 强调色背景，白色文字。Hover/Active 状态有轻微反馈。
        *   **次要按钮:**
            *   浅灰色背景，深灰色文字。
            *   或：透明背景，强调色边框和文字 (幽灵按钮)。
            *   或：纯文本按钮 (强调色文字)。
        *   **图标按钮:** 简洁图标，确保点击区域。
    *   **输入框 (Input Fields):**
        *   圆角: 6px - 8px。
        *   边框: 1px 浅灰色边框 (如 `#D1D1D6`)，或浅灰色背景无边框。
        *   Placeholder 文本颜色使用次要文字色。
        *   Focus 状态: 边框变为强调色，或有轻微阴影。
    *   **标签/徽章 (Tags/Badges):**
        *   圆角: 胶囊状或小圆角矩形。
        *   背景色: 根据信息类型使用柔和的强调色浅色版或中性灰色。
        *   文字颜色: 与背景形成清晰对比。
    *   **下拉菜单/选择器 (Dropdowns/Selects):** 样式与输入框统一，选项列表清晰易选。
    *   **表格 (Tables):**
        *   表头清晰，与内容行有视觉区分。
        *   行间距适中，确保可读性。
        *   斑马纹或 Hover 高亮可选。
    *   **模态框/弹窗 (Modals/Popups):**
        *   圆角，柔和阴影。
        *   内容区域与操作按钮区域清晰划分。
        *   考虑毛玻璃背景效果 (技术评估)。

*   **4.6 图标 (Icons):**
    *   风格: 简洁、线条感强、表意明确 (参考 Heroicons, Feather Icons, Lucide, 或类 SF Symbols 风格)。
    *   尺寸: 与文字和组件大小协调，保持一致性。
    *   颜色: 通常使用文字颜色或强调色。

**5. 具体页面/模块优化需求 (示例)**

*   **5.1 登录/注册页:**
    *   **UI-LOGIN-001:** 标题 ("歡迎回來", "用戶註冊") 字体加大加粗。
    *   **UI-LOGIN-002:** 卡片容器增加圆角 (12px)，应用柔和阴影。
    *   **UI-LOGIN-003:** 输入框样式遵循全局规范 (圆角、边框、Focus 状态)。
    *   **UI-LOGIN-004:** 主要操作按钮 ("登錄", "註冊") 使用主要按钮样式。
    *   **UI-LOGIN-005:** 次要链接 ("立即註冊", "立即登錄") 使用文本链接样式 (强调色)。
    *   **UI-LOGIN-006 (可选):** 增加简洁的品牌 Logo。

*   **5.2 侧边栏导航 (全局):**
    *   **UI-NAV-001:** 整体背景色调整，考虑毛玻璃效果。
    *   **UI-NAV-002:** 菜单项增加垂直间距，提供呼吸感。
    *   **UI-NAV-003:** 为每个菜单项配备风格统一的图标。
    *   **UI-NAV-004:** 菜单项选中状态：背景使用强调色浅色版或强调色胶囊状背景，文字颜色相应调整。
    *   **UI-NAV-005:** 侧边栏底部用户信息区域重新设计：
        *   包含圆形用户头像占位符。
        *   清晰展示用户名和角色。
        *   原圆形按钮如为主题切换，使用明确图标 (太阳/月亮) 和交互反馈。
    *   **UI-NAV-006:** 菜单项分组之间可使用细微分割线或小标题区分。

*   **5.3 人格管理页 (图1):**
    *   **UI-PM-001:** 页面标题 ("人格管理") 样式遵循全局规范。
    *   **UI-PM-002:** "創建新人格" 按钮使用主要按钮样式，可考虑放置在更显眼位置。
    *   **UI-PM-003:** "懦弱" (示例) 卡片：
        *   卡片样式遵循全局规范 (圆角、阴影、内边距)。
        *   卡片标题 ("懦弱") 字体、字重调整。
        *   "預設" 标签样式更新 (圆角、颜色)。
        *   操作按钮 ("編輯", "刪除") 使用次要按钮样式 (如浅灰填充或文本按钮)，增加按钮间距。
        *   "當前" 状态的视觉表现优化 (如使用醒目但非操作性的标签，或不同背景色)。
    *   **UI-PM-004:** 若人格卡片过多，考虑分页或虚拟滚动。

*   **5.4 對話页 (图2):**
    *   **UI-CHAT-001:** "選擇人格" 区域的 "懦弱" (示例) 标签/选择器样式优化，使其更具交互感。
    *   **UI-CHAT-002:** 聊天输入框样式遵循全局规范。
    *   **UI-CHAT-003:** 发送按钮样式优化 (可为图标按钮或带文字的主要按钮)。
    *   **UI-CHAT-004:** 聊天内容区域空状态设计：友好提示语 (如 "選擇一個人格開始對話吧！") 及相关插图/图标。
    *   **UI-CHAT-005:** 聊天气泡样式优化 (圆角、颜色、间距)。

*   **5.5 成長檔案页 (图3):**
    *   **UI-ARCHIVE-001:** 顶部统计卡片样式优化 (圆角、阴影、内边距)，突出核心数据 (加大加粗数字)，文字描述辅助。
    *   **UI-ARCHIVE-002:** "搜尋對話記錄..." 输入框和 "近一週" 日期选择器样式遵循全局规范。
    *   **UI-ARCHIVE-003:** "成長趨勢" 图表区域：
        *   确保图表配色与整体风格协调。
        *   加载状态使用骨架屏或 Loading 指示。
        *   无数据时提供明确提示。
    *   **UI-ARCHIVE-004:** "對話記錄" 列表/表格样式优化，确保可读性和易用性。

*   **5.6 AI 配置管理页 (图4):**
    *   **UI-AICFG-001:** 顶部操作按钮 ("批量操作", "添加新配置")，将 "添加新配置" 作为主要按钮样式。
    *   **UI-AICFG-002:** 配置项卡片样式遵循全局规范 (圆角、阴影、内边距)。
    *   **UI-AICFG-003:** 卡片内信息排版优化，确保清晰、规整，可考虑多列布局。
    *   **UI-AICFG-004:** "禁用"/"啟用" 标签样式更新，使用状态色，更醒目。
    *   **UI-AICFG-005:** "編輯"、"刪除" 按钮使用次要按钮样式。

**6. 技术实现方案建议**

*   **6.1 CSS 方案:**
    *   推荐使用 **Styled-components** 或 **Tailwind CSS**，便于实现组件化样式和主题定制。
    *   若选择 Tailwind CSS，需在 `tailwind.config.js` 中深度定制主题。
    *   若选择 Styled-components，需定义 `theme.ts` 并使用 `ThemeProvider`。
*   **6.2 组件库:**
    *   可基于选定的 CSS 方案自建基础组件库。
    *   或选用现有 React UI 库 (如 Mantine UI, Chakra UI, Headless UI) 并进行深度主题定制。
*   **6.3 全局 CSS 变量:** 定义并使用 CSS 变量管理核心设计元素 (颜色、字体、间距等)，方便全局调整和维护。
*   **6.4 图标库:** 引入 Heroicons, Feather Icons, Lucide 或 React-icons，并确保风格统一。
*   **6.5 字体引入:** 通过 `@font-face` 引入指定字体。
*   **6.6 响应式设计:** 考虑不同屏幕尺寸下的显示效果，确保良好体验 (虽然后台系统主要在桌面端使用，但基础响应式仍有必要)。

**7. 验收标准**

*   **7.1 视觉层面:**
    *   整体界面风格符合定义的 Apple 设计规范 (简洁、干净、现代)。
    *   色彩、字体、间距、圆角、阴影等视觉元素应用统一且协调。
    *   信息层级清晰，重点突出。
*   **7.2 交互层面:**
    *   操作流程顺畅，引导性强。
    *   按钮、输入框等交互组件反馈明确。
    *   不同状态 (正常、Hover、Active、Disabled、Error) 均有清晰视觉表现。
*   **7.3 功能层面:**
    *   所有原有功能不受影响，且在新 UI 下正常工作。
*   **7.4 性能层面:**
    *   页面加载速度和交互响应速度不因 UI 调整而显著下降。
    *   （若使用毛玻璃等效果）需评估性能影响。
*   **7.5 跨浏览器兼容性:**
    *   在主流现代浏览器 (Chrome, Firefox, Safari, Edge 最新版) 上表现一致。

**8. 里程碑与排期 (示例)**

*   **阶段一: 设计与规范定义 (X 周)**
    *   完成详细视觉稿设计 (Figma/Sketch)。
    *   确定并文档化 UI 设计规范 (颜色、字体、组件等)。
    *   技术选型确认 (CSS 方案、组件库方案)。
*   **阶段二: 基础组件开发与主题配置 (X 周)**
    *   开发/配置核心基础 UI 组件 (Button, Card, Input, Nav 等)。
    *   搭建 Storybook (可选，推荐) 进行组件展示和测试。
*   **阶段三: 页面改造与集成 (X 周/模块)**
    *   逐个页面/模块应用新的 UI 组件和样式。
    *   进行功能回归测试。
*   **阶段四: 测试与优化 (X 周)**
    *   内部测试，收集反馈。
    *   性能优化和 Bug 修复。
*   **阶段五: 上线发布**

**9. 风险与应对**

*   **风险1:** Apple 风格细节较多，实现成本可能较高。
    *   **应对:** 优先实现核心风格要素，逐步迭代完善细节。合理利用现有 UI 库的主题定制能力。
*   **风险2:** 前端开发人员对新风格或选定技术方案不熟悉。
    *   **应对:** 提供学习资源和时间，组织内部技术分享。
*   **风险3:** UI 调整可能引入新的 Bug。
    *   **应对:** 加强测试，特别是功能回归测试和视觉走查。
*   **风险4:** （若使用毛玻璃等高级效果）性能和兼容性问题。
    *   **应对:** 充分测试，提供降级方案。
