import React, { ReactNode, ButtonHTMLAttributes } from 'react';
import styled, { css } from 'styled-components';


interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'text';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: ReactNode;
  onClick?: () => void;
}

const StyledButton = styled.button<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${(props) => props.theme.spacing.sm};
  font-family: ${(props) => props.theme.typography.button.fontFamily};
  font-weight: ${(props) => props.theme.typography.button.fontWeight};
  line-height: ${(props) => props.theme.typography.button.lineHeight};
  letter-spacing: ${(props) => props.theme.typography.button.letterSpacing};
  text-decoration: none;
  white-space: nowrap;
  border-radius: ${(props) => props.theme.radius.sm}; // Apple风格 6px-8px
  cursor: pointer;
  user-select: none;
  transition: all 0.15s ease-out; // Apple风格更快的过渡
  position: relative;
  overflow: hidden;
  border: none;

  /* 尺寸样式 - 根据Apple HIG调整 */
  ${(props) => {
    switch (props.size) {
      case 'sm':
        return css`
          font-size: ${props.theme.typography.buttonSmall.fontSize};
          font-weight: ${props.theme.typography.buttonSmall.fontWeight};
          line-height: ${props.theme.typography.buttonSmall.lineHeight};
          padding: ${props.theme.spacing.sm} ${props.theme.spacing.lg};
          min-height: 32px;
          border-radius: ${props.theme.radius.sm};
        `;
      case 'lg':
        return css`
          font-size: ${props.theme.typography.button.fontSize};
          font-weight: ${props.theme.typography.button.fontWeight};
          line-height: ${props.theme.typography.button.lineHeight};
          padding: ${props.theme.spacing.lg} ${props.theme.spacing.xxl};
          min-height: 48px;
          border-radius: ${props.theme.radius.md};
        `;
      default: // md
        return css`
          font-size: ${props.theme.typography.button.fontSize};
          font-weight: ${props.theme.typography.button.fontWeight};
          line-height: ${props.theme.typography.button.lineHeight};
          padding: ${props.theme.spacing.md} ${props.theme.spacing.xl};
          min-height: 40px;
          border-radius: ${props.theme.radius.sm};
        `;
    }
  }}

  /* Apple风格变体样式 */
  ${(props) => {
    switch (props.variant) {
      case 'secondary':
        // 次要按钮 - 浅灰色背景，深灰色文字
        return css`
          background-color: ${props.theme.colors.surfaceSecondary};
          color: ${props.theme.colors.text};
          border: none;

          &:hover:not(:disabled) {
            background-color: ${props.theme.colors.separator};
            transform: translateY(-1px);
          }

          &:active:not(:disabled) {
            background-color: ${props.theme.colors.border};
            transform: translateY(0px);
          }
        `;
      case 'ghost':
        // 幽灵按钮 - 透明背景，强调色边框和文字
        return css`
          background-color: transparent;
          color: ${props.theme.colors.primary};
          border: 1px solid ${props.theme.colors.primary};

          &:hover:not(:disabled) {
            background-color: ${props.theme.colors.primary};
            color: ${props.theme.colors.surface};
            transform: translateY(-1px);
          }

          &:active:not(:disabled) {
            transform: translateY(0px);
          }
        `;
      case 'text':
        // 纯文本按钮 - 强调色文字
        return css`
          background-color: transparent;
          color: ${props.theme.colors.primary};
          border: none;
          padding: ${props.theme.spacing.sm} ${props.theme.spacing.md};

          &:hover:not(:disabled) {
            background-color: ${props.theme.colors.surfaceSecondary};
            transform: translateY(-1px);
          }

          &:active:not(:disabled) {
            transform: translateY(0px);
          }
        `;
      default: // primary
        // 主要按钮 - 强调色背景，白色文字
        return css`
          background-color: ${props.theme.colors.primary};
          color: ${props.theme.colors.surface};
          border: none;

          &:hover:not(:disabled) {
            background-color: ${props.theme.colors.primary};
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: ${props.theme.shadows.sm};
          }

          &:active:not(:disabled) {
            opacity: 0.8;
            transform: translateY(0px);
          }
        `;
    }
  }}

  /* Apple风格禁用状态 */
  &:disabled {
    opacity: 0.3; // Apple风格更低的透明度
    cursor: not-allowed;
    pointer-events: none;
    transform: none !important;
  }

  /* 加载状态 */
  ${({ loading }) => loading && css`
    color: transparent;
    pointer-events: none;
  `}

  /* Apple风格焦点样式 */
  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
    border-radius: ${({ theme }) => theme.radius.sm};
  }

  /* 移除默认的活动状态，使用变体中定义的 */
`;
`;

// 加载动画组件
const LoadingSpinner = styled.div<{ size?: ButtonProps['size'] }>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: button-loading-spinner 1s linear infinite;

  @keyframes button-loading-spinner {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
  }
`;

export const Button = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  children,
  onClick,
  type = 'button',
  ...props
}: ButtonProps) => {

  return (
    <StyledButton
      variant={variant}
      size={size}
      loading={loading}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
      aria-disabled={disabled || loading}
      aria-busy={loading}
      {...props}
    >
      <span style={{ opacity: loading ? 0 : 1 }}>
        {children}
      </span>

      {loading && <LoadingSpinner size={size} />}
    </StyledButton>
  );
};