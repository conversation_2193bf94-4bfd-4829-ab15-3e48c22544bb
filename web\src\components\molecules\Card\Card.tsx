import React, { ReactNode } from 'react';
import styled, { DefaultTheme } from 'styled-components';
import { useTheme } from 'design-system/theme/ThemeProvider';
import { motion, HTMLMotionProps } from 'framer-motion';

interface CardProps extends HTMLMotionProps<'div'> {
  children: ReactNode;
  as?: any; // 允许as属性用于motion.div
}

interface StyledCardProps extends HTMLMotionProps<'div'> {
  theme: DefaultTheme;
}

const StyledCard = styled(motion.div)<StyledCardProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.radius.xl}; // Apple风格 12px卡片圆角
  box-shadow: ${(props) => props.theme.shadows.sm}; // PRD推荐的柔和阴影
  padding: ${(props) => props.theme.spacing.cardPadding}; // PRD推荐16-24px
  transition: all 0.15s ease-out; // Apple风格更快的过渡
  border: none; // Apple风格通常无边框，依靠阴影区分层级
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${(props) => props.theme.shadows.md}; // 悬浮时增强阴影
  }

  /* 为卡片内容提供更好的呼吸空间 */
  > * + * {
    margin-top: ${(props) => props.theme.spacing.lg};
  }
`;

export const Card = ({ children, ...props }: CardProps) => {
  const { theme } = useTheme();

  return (
    <StyledCard {...props}>
      {children}
    </StyledCard>
  );
}; 