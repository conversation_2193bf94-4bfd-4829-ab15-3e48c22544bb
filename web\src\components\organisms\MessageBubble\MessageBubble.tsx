import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { Typography } from '../../atoms';

interface MessageBubbleProps {
  message: {
    id: string;
    content: string;
    sender: 'user' | 'ai';
    timestamp: Date;
    persona?: string;
  };
  isAnimating?: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isAnimating }) => {
  const isUser = message.sender === 'user';

  return (
    <BubbleContainer isUser={isUser}>
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{
          duration: 0.3,
          ease: "easeOut",
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
        style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: isUser ? 'flex-end' : 'flex-start' }}
      >
        <MessageWrapper isUser={isUser}>
          {!isUser && message.persona && (
            <PersonaLabel>{message.persona}</PersonaLabel>
          )}
          <BubbleContent isUser={isUser}>
            {message.content}
          </BubbleContent>
          <Timestamp isUser={isUser}>
            {message.timestamp.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            })}
          </Timestamp>
        </MessageWrapper>
      </motion.div>
    </BubbleContainer>
  );
};

// Apple风格消息气泡 - PRD要求优化聊天气泡样式
const BubbleContainer = styled.div<{ isUser: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: ${({ isUser }) => (isUser ? 'flex-end' : 'flex-start')};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  max-width: 100%;
`;

const MessageWrapper = styled.div<{ isUser: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: ${({ isUser }) => (isUser ? 'flex-end' : 'flex-start')};
  max-width: 75%;

  @media (max-width: 768px) {
    max-width: 85%;
  }
`;

const BubbleContent = styled.div<{ isUser: boolean }>`
  background-color: ${({ theme, isUser }) =>
    isUser ? theme.colors.primary : theme.colors.surface};
  color: ${({ theme, isUser }) =>
    isUser ? theme.colors.surface : theme.colors.text};
  padding: ${({ theme }) => theme.spacing.md} ${({ theme }) => theme.spacing.lg};
  border-radius: ${({ theme, isUser }) =>
    isUser
      ? `${theme.radius.xl} ${theme.radius.xl} ${theme.radius.sm} ${theme.radius.xl}` // 用户消息右下角小圆角
      : `${theme.radius.xl} ${theme.radius.xl} ${theme.radius.xl} ${theme.radius.sm}`}; // AI消息左下角小圆角
  word-wrap: break-word;
  line-height: 1.5;
  box-shadow: ${({ theme, isUser }) =>
    isUser ? theme.shadows.sm : theme.shadows.xs};
  border: ${({ theme, isUser }) =>
    isUser ? 'none' : `1px solid ${theme.colors.separator}`};
  position: relative;

  /* 文字样式 */
  p {
    margin: 0;
    font-size: ${({ theme }) => theme.typography.body.fontSize};
    line-height: ${({ theme }) => theme.typography.body.lineHeight};
  }

  /* 链接样式 */
  a {
    color: ${({ theme, isUser }) =>
      isUser ? theme.colors.surface : theme.colors.primary};
    text-decoration: underline;
  }
`;

const PersonaLabel = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.typography.caption1.fontSize};
  font-weight: 500;
  margin-bottom: ${({ theme }) => theme.spacing.xs};
  padding-left: ${({ theme }) => theme.spacing.sm};
`;

const Timestamp = styled.div<{ isUser: boolean }>`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.typography.caption2.fontSize};
  margin-top: ${({ theme }) => theme.spacing.xs};
  padding: 0 ${({ theme }) => theme.spacing.sm};
  opacity: 0.7;
`;

export default MessageBubble; 