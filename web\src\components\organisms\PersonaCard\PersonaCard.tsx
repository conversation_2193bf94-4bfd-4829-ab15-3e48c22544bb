import React from 'react';
import styled, { DefaultTheme } from 'styled-components';
import { motion } from 'framer-motion';
import { Button, Typography } from '../../atoms'; // Assuming Button and Typography are from atoms
import { Card } from '../../molecules'; // Assuming Card is from molecules
import { useTheme } from 'design-system/theme/ThemeProvider';
import { Persona } from '../../../types/persona'; // Import shared Persona type
import LazyImage from '../../common/LazyImage'; // Import LazyImage

interface PersonaCardProps {
  persona: Persona;
  onEdit: (p: Persona) => void;
  onDelete: (id: string) => void;
  isCurrent: boolean;
  onSwitch: (id: string) => void;
}

// Apple风格PersonaCard组件样式
const CardContainer = styled(Card)<{ isCurrent: boolean }>`
  text-align: center;
  position: relative;
  transition: all 0.15s ease-out;

  /* PRD要求："當前"状态的视觉表现优化 */
  ${({ isCurrent, theme }) => isCurrent && `
    border: 2px solid ${theme.colors.primary};
    box-shadow: 0 0 0 4px ${theme.colors.primary}20;
  `}
`;

const Avatar = styled(LazyImage)`
  width: 80px;
  height: 80px;
  border-radius: ${(props) => props.theme.radius.round}; // 完全圆形
  object-fit: cover;
  margin: 0 auto ${(props) => props.theme.spacing.lg};
  display: block;
  border: 3px solid ${(props) => props.theme.colors.surface};
  box-shadow: ${(props) => props.theme.shadows.xs};
`;

const PersonaName = styled(Typography)`
  margin-bottom: ${(props) => props.theme.spacing.sm};
  color: ${(props) => props.theme.colors.text};
`;

const PersonaDescription = styled(Typography)`
  margin-bottom: ${(props) => props.theme.spacing.lg};
  color: ${(props) => props.theme.colors.textSecondary};
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

// PRD要求：标签样式更新
const TagContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${(props) => props.theme.spacing.xs};
  justify-content: center;
  margin-bottom: ${(props) => props.theme.spacing.xl};
`;

const Tag = styled.span`
  background-color: ${(props) => props.theme.colors.surfaceSecondary};
  color: ${(props) => props.theme.colors.text};
  padding: ${(props) => props.theme.spacing.xs} ${(props) => props.theme.spacing.sm};
  border-radius: ${(props) => props.theme.radius.full}; // 胶囊状
  font-size: ${(props) => props.theme.typography.caption1.fontSize};
  font-weight: 500;
  border: 1px solid ${(props) => props.theme.colors.separator};
  transition: all 0.15s ease-out;

  &:hover {
    background-color: ${(props) => props.theme.colors.separator};
  }
`;

// PRD要求：操作按钮样式优化，增加按钮间距
const ActionButtons = styled.div`
  display: flex;
  justify-content: center;
  gap: ${(props) => props.theme.spacing.md}; // 增加按钮间距
  margin-top: auto;
  padding-top: ${(props) => props.theme.spacing.lg};
  border-top: 1px solid ${(props) => props.theme.colors.separator};
`;

// 当前状态指示器
const CurrentBadge = styled.div`
  position: absolute;
  top: ${(props) => props.theme.spacing.md};
  right: ${(props) => props.theme.spacing.md};
  background-color: ${(props) => props.theme.colors.primary};
  color: ${(props) => props.theme.colors.surface};
  padding: ${(props) => props.theme.spacing.xs} ${(props) => props.theme.spacing.sm};
  border-radius: ${(props) => props.theme.radius.full};
  font-size: ${(props) => props.theme.typography.caption2.fontSize};
  font-weight: 600;
  box-shadow: ${(props) => props.theme.shadows.sm};
`;

export const PersonaCard = ({
  persona,
  onEdit,
  onDelete,
  onSwitch,
  isCurrent,
  ...props
}: PersonaCardProps) => {
  const { theme } = useTheme();

  return (
    <CardContainer
      isCurrent={isCurrent}
      whileHover={{ scale: 1.02 }}
      {...props}
    >
      {/* PRD要求：当前状态的视觉表现优化 */}
      {isCurrent && <CurrentBadge>當前</CurrentBadge>}

      <Avatar src={persona.avatar} alt={persona.name} />

      {/* PRD要求：卡片标题字体、字重调整 */}
      <PersonaName variant="title3">{persona.name}</PersonaName>

      <PersonaDescription variant="callout">
        {persona.description || '暂无描述'}
      </PersonaDescription>

      {/* PRD要求：标签样式更新 */}
      <TagContainer>
        {persona.traits?.map((trait: string) => (
          <Tag key={trait}>{trait}</Tag>
        ))}
      </TagContainer>

      {/* PRD要求：操作按钮使用次要按钮样式，增加按钮间距 */}
      <ActionButtons>
        <Button variant="secondary" size="sm" onClick={() => onEdit(persona)}>
          編輯
        </Button>
        <Button variant="secondary" size="sm" onClick={() => onDelete(persona.id)}>
          刪除
        </Button>
        <Button
          variant={isCurrent ? "text" : "primary"}
          size="sm"
          onClick={() => onSwitch(persona.id)}
          disabled={isCurrent}
        >
          {isCurrent ? '已選中' : '切換'}
        </Button>
      </ActionButtons>
    </CardContainer>
  );
};