import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { media } from '../../../design-system/utils/styleUtils';
import { useTheme } from 'design-system/theme/ThemeProvider';
import { motion } from 'framer-motion';

interface PersonaGridProps {
  children: ReactNode;
}

// Apple风格人格网格布局
const StyledPersonaGrid = styled.div`
  display: grid;
  gap: ${(props) => props.theme.spacing.xl}; // 增加卡片间距，提供更好的呼吸感
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); // 稍微增加最小宽度
  margin-top: ${(props) => props.theme.spacing.xl};

  /* 响应式设计 */
  ${media.tablet`
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: ${(props) => props.theme.spacing.lg};
  `}

  ${media.mobile`
    grid-template-columns: 1fr;
    gap: ${(props) => props.theme.spacing.lg};
  `}

  /* 空状态样式 */
  &:empty::after {
    content: "暂无人格，点击上方按钮创建第一个人格";
    grid-column: 1 / -1;
    text-align: center;
    padding: ${(props) => props.theme.spacing.xxxl};
    color: ${(props) => props.theme.colors.textSecondary};
    font-size: ${(props) => props.theme.typography.callout.fontSize};
    background-color: ${(props) => props.theme.colors.surfaceSecondary};
    border-radius: ${(props) => props.theme.radius.xl};
    border: 2px dashed ${(props) => props.theme.colors.separator};
  }
`;

export const PersonaGrid = ({ children, ...props }: PersonaGridProps) => {
  const { theme } = useTheme();
  return (
    <StyledPersonaGrid {...props}>
      {children}
    </StyledPersonaGrid>
  );
}; 