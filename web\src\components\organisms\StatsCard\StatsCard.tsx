import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { Typography } from '../../atoms';
import { Card } from '../../molecules'; // Assuming Card is in molecules
import { Icon } from '../../atoms'; // Icon is now in atoms

interface StatsCardProps {
  title: string;
  value: number;
  unit: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: number;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  unit,
  trend,
  trendValue,
}) => {
  return (
    <StyledCard
      whileHover={{ scale: 1.02, y: -2 }}
      transition={{ duration: 0.15, ease: "easeOut" }}
    >
      <CardTitle>{title}</CardTitle>
      <ValueContainer>
        <MainValue>{value}</MainValue>
        <UnitText>{unit}</UnitText>
      </ValueContainer>
      {trend && trendValue !== undefined && (
        <TrendContainer trend={trend}>
          <Icon name={trend === 'up' ? 'arrow-up' : 'arrow-down'} size="14px" />
          <TrendText trend={trend}>
            {trend === 'up' ? '+' : trend === 'down' ? '-' : ''}{trendValue}%
          </TrendText>
        </TrendContainer>
      )}
    </StyledCard>
  );
};

// Apple风格统计卡片样式 - PRD要求优化卡片样式
const StyledCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.cardPadding};
  border-radius: ${({ theme }) => theme.radius.xl};
  min-width: 220px;
  text-align: center;
  background-color: ${({ theme }) => theme.colors.surface};
  border: none;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  position: relative;
  overflow: hidden;

  /* 添加微妙的渐变背景 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      ${({ theme }) => theme.colors.primary},
      ${({ theme }) => theme.colors.secondary}
    );
  }
`;

const CardTitle = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.typography.caption1.fontSize};
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const ValueContainer = styled.div`
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const MainValue = styled.div`
  font-size: ${({ theme }) => theme.typography.title1.fontSize};
  font-weight: ${({ theme }) => theme.typography.title1.fontWeight};
  color: ${({ theme }) => theme.colors.text};
  line-height: 1;
`;

const UnitText = styled.div`
  font-size: ${({ theme }) => theme.typography.callout.fontSize};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-weight: 400;
`;

const TrendContainer = styled.div<{ trend: 'up' | 'down' | 'stable' }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border-radius: ${({ theme }) => theme.radius.full};
  background-color: ${({ theme, trend }) =>
    trend === 'up'
      ? `${theme.colors.green}15`
      : trend === 'down'
      ? `${theme.colors.red}15`
      : `${theme.colors.textSecondary}15`};

  svg {
    fill: ${({ theme, trend }) =>
      trend === 'up'
        ? theme.colors.green
        : trend === 'down'
        ? theme.colors.red
        : theme.colors.textSecondary};
  }
`;

const TrendText = styled.div<{ trend: 'up' | 'down' | 'stable' }>`
  font-size: ${({ theme }) => theme.typography.caption1.fontSize};
  font-weight: 600;
  color: ${({ theme, trend }) =>
    trend === 'up'
      ? theme.colors.green
      : trend === 'down'
      ? theme.colors.red
      : theme.colors.textSecondary};
`;

export default StatsCard; 