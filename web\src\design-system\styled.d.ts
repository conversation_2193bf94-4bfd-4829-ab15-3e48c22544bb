import 'styled-components';

declare module 'styled-components' {
  export interface DefaultTheme {
    colors: {
      // 主要强调色
      primary: string;
      secondary: string;
      tertiary: string;
      quaternary: string;

      // 背景色系统
      background: string;
      surface: string;
      surfaceSecondary: string;

      // 文字色系统
      text: string;
      textSecondary: string;
      textTertiary: string;

      // 边框和分割线
      separator: string;
      border: string;

      // 状态色系统
      red: string;
      green: string;
      blue: string;
      yellow: string;
      orange: string;
      purple: string;
      pink: string;
      teal: string;
      indigo: string;
      brown: string;
      gray: string;
    };
    typography: {
      largeTitle: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      title1: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      title2: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      title3: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      headline: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      body: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      callout: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      subhead: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      footnote: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      caption1: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
      caption2: {
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
        fontFamily: string;
      };
    };
    spacing: {
      none: string;
      xxs: string;
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      xxl: string;
      xxxl: string;
    };
    shadows: {
      none: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    radius: {
      none: string;
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      round: string;
      full: string;
    };
  }
}