// Apple风格颜色系统 - 根据PRD要求设计
export const lightColors = {
  // 主要强调色 - Apple蓝
  primary: '#007AFF',
  secondary: '#5856D6', // 紫色作为次要强调色
  tertiary: '#32D74B', // 绿色
  quaternary: '#FF9500', // 橙色

  // 背景色系统
  background: '#F5F5F7', // 主背景色 - 非常浅的灰色
  surface: '#FFFFFF', // 卡片/内容区背景色 - 纯白
  surfaceSecondary: '#F2F2F7', // 次要表面色

  // 文字色系统
  text: '#1D1D1F', // 主要文字色 - 深灰色
  textSecondary: '#6E6E73', // 次要文字/辅助信息色 - 中灰色
  textTertiary: '#8E8E93', // 三级文字色

  // 边框和分割线
  separator: '#D1D1D6', // 分割线颜色
  border: '#E5E5EA', // 边框颜色

  // 状态色系统
  red: '#FF3B30', // 错误/禁用
  green: '#34C759', // 成功/启用
  blue: '#007AFF', // 信息
  yellow: '#FFCC00', // 警告
  orange: '#FF9500', // 警告/待定
  purple: '#5856D6', // 装饰色
  pink: '#FF2D92', // 装饰色
  teal: '#5AC8FA', // 装饰色
  indigo: '#5856D6', // 装饰色
  brown: '#A2845E', // 装饰色
  gray: '#8E8E93', // 中性灰
};

export const darkColors = {
  // 主要强调色 - 在深色模式下稍微调亮
  primary: '#0A84FF',
  secondary: '#5E5CE6',
  tertiary: '#30D158',
  quaternary: '#FF9F0A',

  // 背景色系统
  background: '#000000', // 主背景色 - 纯黑
  surface: '#1C1C1E', // 卡片/内容区背景色 - 深灰
  surfaceSecondary: '#2C2C2E', // 次要表面色

  // 文字色系统
  text: '#FFFFFF', // 主要文字色 - 纯白
  textSecondary: '#EBEBF5', // 次要文字色，透明度60%
  textTertiary: '#EBEBF5', // 三级文字色，透明度30%

  // 边框和分割线
  separator: '#38383A', // 分割线颜色
  border: '#48484A', // 边框颜色

  // 状态色系统
  red: '#FF453A',
  green: '#32D74B',
  blue: '#0A84FF',
  yellow: '#FFD60A',
  orange: '#FF9F0A',
  purple: '#5E5CE6',
  pink: '#FF375F',
  teal: '#64D2FF',
  indigo: '#5E5CE6',
  brown: '#AC8E68',
  gray: '#8E8E93',
};



export type Colors = typeof lightColors; 