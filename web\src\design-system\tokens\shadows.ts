// Apple风格阴影系统 - 柔和、弥散的灰色阴影
export const shadows = {
  none: 'none',
  // 非常轻微的阴影，用于悬浮状态
  xs: '0px 1px 3px rgba(0, 0, 0, 0.05)',
  // 标准卡片阴影 - PRD推荐的柔和阴影
  sm: '0px 4px 12px rgba(0, 0, 0, 0.05), 0px 1px 3px rgba(0, 0, 0, 0.08)',
  // 中等阴影，用于悬浮卡片
  md: '0px 8px 24px rgba(0, 0, 0, 0.08), 0px 2px 6px rgba(0, 0, 0, 0.12)',
  // 较强阴影，用于模态框
  lg: '0px 16px 48px rgba(0, 0, 0, 0.12), 0px 4px 12px rgba(0, 0, 0, 0.16)',
  // 最强阴影，用于弹出层
  xl: '0px 24px 64px rgba(0, 0, 0, 0.16), 0px 8px 24px rgba(0, 0, 0, 0.20)',
  // 特大阴影，用于全屏覆盖
  xxl: '0px 32px 80px rgba(0, 0, 0, 0.20), 0px 12px 32px rgba(0, 0, 0, 0.24)',
};

export type Shadows = typeof shadows; 