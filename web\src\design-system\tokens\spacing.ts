// Apple风格间距系统 - 基于4px/8px倍数，根据PRD要求设计
export const spacing = {
  none: '0px',
  // 基础间距单位 - 4px倍数
  xxs: '2px', // 极小间距
  xs: '4px', // 最小间距
  sm: '8px', // 小间距
  md: '12px', // 中小间距
  lg: '16px', // 标准间距
  xl: '20px', // 中大间距
  xxl: '24px', // 大间距
  xxxl: '32px', // 超大间距
  xxxxl: '48px', // 特大间距
  xxxxxl: '64px', // 巨大间距

  // 特殊用途间距
  cardPadding: '20px', // 卡片内边距 - PRD推荐16-24px
  cardPaddingLarge: '24px', // 大卡片内边距
  sectionSpacing: '32px', // 区域间距
  pageSpacing: '24px', // 页面边距
};

export type Spacing = typeof spacing;