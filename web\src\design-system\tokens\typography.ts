// Apple风格排版系统 - 根据PRD要求，引入SF Pro字体系列
const fontFamily = {
  // 首选SF Pro字体，备选Inter、Manrope、Noto Sans SC
  primary: '"SF Pro Text", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Inter", "Manrope", "Noto Sans SC", "Segoe UI", Roboto, sans-serif',
  display: '"SF Pro Display", "SF Pro Text", -apple-system, BlinkMacSystemFont, "Inter", "Manrope", "Segoe UI", Roboto, sans-serif',
};

export const typography = {
  // 页面大标题 - PRD推荐28-34px, Semi-bold/Bold
  largeTitle: {
    fontSize: '34px',
    fontWeight: '700', // Bold
    lineHeight: '41px', // 1.2倍行高
    letterSpacing: '-0.41px',
    fontFamily: fontFamily.display,
  },
  // 页面标题
  title1: {
    fontSize: '28px',
    fontWeight: '600', // Semi-bold
    lineHeight: '34px', // 1.2倍行高
    letterSpacing: '-0.35px',
    fontFamily: fontFamily.display,
  },
  // 区域/卡片标题 - PRD推荐17-20px, Medium/Semi-bold
  title2: {
    fontSize: '22px',
    fontWeight: '600', // Semi-bold
    lineHeight: '28px', // 1.27倍行高
    letterSpacing: '-0.26px',
    fontFamily: fontFamily.display,
  },
  title3: {
    fontSize: '20px',
    fontWeight: '600', // Semi-bold
    lineHeight: '25px', // 1.25倍行高
    letterSpacing: '-0.24px',
    fontFamily: fontFamily.display,
  },
  // 区域标题
  headline: {
    fontSize: '17px',
    fontWeight: '500', // Medium
    lineHeight: '22px', // 1.29倍行高
    letterSpacing: '-0.41px',
    fontFamily: fontFamily.primary,
  },
  // 正文/描述 - PRD推荐14-16px, Regular
  body: {
    fontSize: '16px',
    fontWeight: '400', // Regular
    lineHeight: '24px', // 1.5倍行高
    letterSpacing: '-0.32px',
    fontFamily: fontFamily.primary,
  },
  // 次要正文
  callout: {
    fontSize: '15px',
    fontWeight: '400', // Regular
    lineHeight: '21px', // 1.4倍行高
    letterSpacing: '-0.24px',
    fontFamily: fontFamily.primary,
  },
  // 子标题
  subhead: {
    fontSize: '14px',
    fontWeight: '400', // Regular
    lineHeight: '20px', // 1.43倍行高
    letterSpacing: '-0.08px',
    fontFamily: fontFamily.primary,
  },
  // 脚注
  footnote: {
    fontSize: '13px',
    fontWeight: '400', // Regular
    lineHeight: '18px', // 1.38倍行高
    letterSpacing: '-0.08px',
    fontFamily: fontFamily.primary,
  },
  // 标签/辅助文字 - PRD推荐12-13px, Regular/Medium
  caption1: {
    fontSize: '12px',
    fontWeight: '400', // Regular
    lineHeight: '16px', // 1.33倍行高
    letterSpacing: '0px',
    fontFamily: fontFamily.primary,
  },
  caption2: {
    fontSize: '11px',
    fontWeight: '400', // Regular
    lineHeight: '13px', // 1.18倍行高
    letterSpacing: '0.07px',
    fontFamily: fontFamily.primary,
  },
  // 按钮文字
  button: {
    fontSize: '16px',
    fontWeight: '500', // Medium
    lineHeight: '20px', // 1.25倍行高
    letterSpacing: '-0.32px',
    fontFamily: fontFamily.primary,
  },
  // 小按钮文字
  buttonSmall: {
    fontSize: '14px',
    fontWeight: '500', // Medium
    lineHeight: '18px', // 1.29倍行高
    letterSpacing: '-0.08px',
    fontFamily: fontFamily.primary,
  },
};

export type Typography = typeof typography;