import React, { useEffect, useState } from 'react';
import axios from 'axios';
import styled from 'styled-components';
import { format } from 'date-fns';

// Import new components
import StatsCard from '../../components/organisms/StatsCard/StatsCard';
import ConversationTimeline from '../../components/organisms/ConversationTimeline/ConversationTimeline';
import GrowthChart from '../../components/organisms/GrowthChart/GrowthChart';
import { SearchBar } from '../../components/molecules/SearchBar/SearchBar'; // Assuming SearchBar is in molecules
import { Select } from '../../components/atoms/Select/Select'; // Select is now in atoms
import { Typography } from '../../components/atoms';

// Assuming a DashboardLayout or PageTransition might be needed, adjust as per design system
// import DashboardLayout from '../../layouts/DashboardLayout'; 
// import { PageTransition } from '../../components/animations';

interface Conversation {
  id: string;
  date: Date;
  persona: string;
  summary: string;
  mood: 'positive' | 'neutral' | 'negative';
  duration: number;
}

interface Stats {
  totalConversations: number;
  activePersonas: number;
  growthIndex: number;
  dailyActivity: { name: string; value: number }[];
}

const ArchivePage: React.FC = () => {
  const [timeRange, setTimeRange] = useState<string>('week');
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [stats, setStats] = useState<Stats>({ totalConversations: 0, activePersonas: 0, growthIndex: 0, dailyActivity: [] });
  const [searchTerm, setSearchTerm] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const chatRes = await axios.get<any>('/api/archive/chats');
        // Map messages to ConversationTimeline's TimelineItem format
        const formattedConversations: Conversation[] = chatRes.data.flatMap((chat: any) =>
          chat.messages.map((m: any, idx: number) => ({
            id: `${chat.id}-${idx}`,
            date: new Date(m.timestamp),
            persona: chat.personas.map((pId: number) => `Persona ${pId}`).join('、'), // Placeholder for persona name
            summary: m.content.substring(0, 50) + '...', // Short summary
            mood: 'neutral', // Placeholder for mood
            duration: Math.floor(Math.random() * 30) + 5, // Random duration for demo
          }))
        );
        setConversations(formattedConversations);

        const reportRes = await axios.get<Stats>('/api/archive/report');
        setStats(reportRes.data);
      } catch (error) {
        console.error('Error fetching archive data:', error);
      }
    };
    fetchData();
  }, [timeRange]); // Re-fetch data when timeRange changes

  const filteredConversations = conversations.filter(conv =>
    conv.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.persona.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <PageContainer>
      <Header>
        <Typography variant="largeTitle">成長檔案</Typography>
        <Controls>
          <SearchBar
            placeholder="搜尋對話記錄..."
            onSearch={setSearchTerm}
          />
          <Select
            value={timeRange}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setTimeRange(e.target.value)}
            options={[
              { value: 'week', label: '近一週' },
              { value: 'month', label: '近一月' },
              { value: 'year', label: '近一年' },
            ]}
          />
        </Controls>
      </Header>

      <StatsSection>
        <StatsCard
          title="總對話次數"
          value={stats.totalConversations}
          unit="次"
          trend="up"
          trendValue={12}
        />
        <StatsCard
          title="活躍人格"
          value={stats.activePersonas}
          unit="個"
        />
        <StatsCard
          title="成長指數"
          value={stats.growthIndex}
          unit="分"
          trend="up"
          trendValue={8}
        />
      </StatsSection>

      <ChartSection>
        <GrowthChart data={stats.dailyActivity} />
      </ChartSection>

      <TimelineSection>
        <Typography variant="title2">對話記錄</Typography>
        <ConversationTimeline items={filteredConversations} />
      </TimelineSection>
    </PageContainer>
  );
};

// Apple风格成长档案页面样式
const PageContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.pageSpacing};
  max-width: 1200px;
  margin: auto;
  background-color: ${({ theme }) => theme.colors.background};
  min-height: 100vh;
`;

// PRD要求：页面标题和控件布局优化
const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing.xxxl};
  padding-bottom: ${({ theme }) => theme.spacing.xl};
  border-bottom: 1px solid ${({ theme }) => theme.colors.separator};

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: ${({ theme }) => theme.spacing.lg};
  }

  h1 {
    margin: 0;
    color: ${({ theme }) => theme.colors.text};
  }
`;

// PRD要求：搜索输入框和日期选择器样式遵循全局规范
const Controls = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.lg};
  align-items: center;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: stretch;

    > * {
      flex: 1;
      min-width: 200px;
    }
  }
`;

// PRD要求：顶部统计卡片样式优化
const StatsSection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: ${({ theme }) => theme.spacing.xl};
  margin-bottom: ${({ theme }) => theme.spacing.xxxl};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing.lg};
  }
`;

// PRD要求："成長趨勢"图表区域样式优化
const ChartSection = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.radius.xl};
  padding: ${({ theme }) => theme.spacing.cardPaddingLarge};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  margin-bottom: ${({ theme }) => theme.spacing.xxxl};
  border: none;

  /* 图表标题 */
  h3 {
    margin: 0 0 ${({ theme }) => theme.spacing.xl} 0;
    color: ${({ theme }) => theme.colors.text};
    font-size: ${({ theme }) => theme.typography.title3.fontSize};
    font-weight: ${({ theme }) => theme.typography.title3.fontWeight};
  }
`;

// PRD要求："對話記錄"列表样式优化
const TimelineSection = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.radius.xl};
  padding: ${({ theme }) => theme.spacing.cardPaddingLarge};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border: none;

  /* 区域标题 */
  h2 {
    margin: 0 0 ${({ theme }) => theme.spacing.xl} 0;
    color: ${({ theme }) => theme.colors.text};
    font-size: ${({ theme }) => theme.typography.title2.fontSize};
    font-weight: ${({ theme }) => theme.typography.title2.fontWeight};
  }
`;

export default ArchivePage; 