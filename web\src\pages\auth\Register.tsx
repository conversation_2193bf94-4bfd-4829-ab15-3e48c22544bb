import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { AuthLayout } from '../../layouts/AuthLayout';
import { Button, Input, Typography } from '../../components/atoms';
import { Card, FormField } from '../../components/molecules';
import { LoadingSpinner } from '../../components/animations/LoadingSpinner';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../design-system/theme/ThemeProvider';

// Apple风格注册页面样式 - 与登录页面保持一致
const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${(props) => props.theme.spacing.xl}; // 增加间距，创造呼吸感
  margin-top: ${(props) => props.theme.spacing.xxl};
`;

// PRD要求：标题加大加粗
const PageTitle = styled(Typography)`
  text-align: center;
  margin-bottom: ${(props) => props.theme.spacing.lg};
  color: ${(props) => props.theme.colors.text};
`;

const ErrorMessage = styled(Typography)`
  margin-top: ${(props) => props.theme.spacing.lg};
  text-align: center;
  padding: ${(props) => props.theme.spacing.md} ${(props) => props.theme.spacing.lg};
  background-color: ${(props) => props.theme.colors.red}10; // 10% 透明度背景
  border-radius: ${(props) => props.theme.radius.sm};
  border-left: 3px solid ${(props) => props.theme.colors.red};
`;

const SuccessMessage = styled(Typography)`
  margin-top: ${(props) => props.theme.spacing.lg};
  text-align: center;
  padding: ${(props) => props.theme.spacing.md} ${(props) => props.theme.spacing.lg};
  background-color: ${(props) => props.theme.colors.green}10; // 10% 透明度背景
  border-radius: ${(props) => props.theme.radius.sm};
  border-left: 3px solid ${(props) => props.theme.colors.green};
`;

const LoginSection = styled.div`
  margin-top: ${(props) => props.theme.spacing.xxl};
  text-align: center;
  padding-top: ${(props) => props.theme.spacing.xl};
  border-top: 1px solid ${(props) => props.theme.colors.separator};
`;

const LoginText = styled(Typography)`
  margin-bottom: ${(props) => props.theme.spacing.lg};
  color: ${(props) => props.theme.colors.textSecondary};
`;

// 品牌区域
const BrandSection = styled.div`
  text-align: center;
  margin-bottom: ${(props) => props.theme.spacing.xxl};
`;

const BrandTitle = styled(Typography)`
  color: ${(props) => props.theme.colors.primary};
  margin-bottom: ${(props) => props.theme.spacing.sm};
`;

const BrandSubtitle = styled(Typography)`
  color: ${(props) => props.theme.colors.textSecondary};
`;

const Register: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [account, setAccount] = useState('');
  const [password, setPassword] = useState('');
  const [confirm, setConfirm] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);
    if (password !== confirm) {
      setError('兩次密碼不一致');
      setLoading(false);
      return;
    }
    try {
      await axios.post('/api/user/register', { account, password });
      setSuccess('註冊成功，請登錄');
    } catch (err: any) {
      setError(err.response?.data?.message || '註冊失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <Card
        as={motion.div}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        {/* PRD要求：可选的品牌Logo */}
        <BrandSection>
          <BrandTitle variant="title2">世另我</BrandTitle>
          <BrandSubtitle variant="caption1">與不同的自己對話</BrandSubtitle>
        </BrandSection>

        {/* PRD要求：标题加大加粗 */}
        <PageTitle variant="largeTitle">用戶註冊</PageTitle>

        <Form onSubmit={handleSubmit}>
          <FormField label="賬號">
            <Input
              type="text"
              placeholder="請輸入賬號"
              value={account}
              onChange={(e) => setAccount(e.target.value)}
              required
            />
          </FormField>
          <FormField label="密碼">
            <Input
              type="password"
              placeholder="請輸入密碼"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </FormField>
          <FormField label="確認密碼">
            <Input
              type="password"
              placeholder="請再次輸入密碼"
              value={confirm}
              onChange={(e) => setConfirm(e.target.value)}
              required
            />
          </FormField>
          {/* PRD要求：主要操作按钮使用主要按钮样式 */}
          <Button
            variant="primary"
            size="lg"
            loading={loading}
            disabled={loading}
            type="submit"
          >
            {loading ? <LoadingSpinner /> : '註冊'}
          </Button>
        </Form>

        {error && (
          <ErrorMessage variant="callout" color={theme.colors.red}>
            {error}
          </ErrorMessage>
        )}
        {success && (
          <SuccessMessage variant="callout" color={theme.colors.green}>
            {success}
          </SuccessMessage>
        )}

        <LoginSection>
          <LoginText variant="callout">
            已有帳號？
          </LoginText>
          {/* PRD要求：次要链接使用文本链接样式 */}
          <Button
            variant="text"
            size="md"
            onClick={() => navigate('/auth/login')}
            disabled={loading}
          >
            立即登錄
          </Button>
        </LoginSection>
      </Card>
    </AuthLayout>
  );
};

export default Register; 